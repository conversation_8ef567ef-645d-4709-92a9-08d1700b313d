import os, base64, uuid
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import requests
from azure.cosmos import CosmosClient, PartitionKey

# ---- Load env values ----
TENANT_ID = os.getenv("TENANT_ID")
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
USER_EMAIL = "<EMAIL>"
COSMOS_URL = os.getenv("COSMOS_URL")
COSMOS_KEY = os.getenv("COSMOS_KEY")
DB_NAME = os.getenv("DATABASE_NAME")
CONTAINER_NAME = os.getenv("CONTAINER_NAME")

# ---- Auth with Microsoft Graph ----
def get_graph_token():
    url = f"https://login.microsoftonline.com/{TENANT_ID}/oauth2/v2.0/token"
    data = {
        "grant_type": "client_credentials",
        "client_id": CLIENT_ID,
        "client_secret": CLIENT_SECRET,
        "scope": "https://graph.microsoft.com/.default"
    }
    r = requests.post(url, data=data)
    r.raise_for_status()
    return r.json()["access_token"]

# ---- Cosmos DB setup ----
cosmos_client = CosmosClient(COSMOS_URL, COSMOS_KEY)
database = cosmos_client.create_database_if_not_exists(DB_NAME)
container = database.create_container_if_not_exists(
    id=CONTAINER_NAME,
    partition_key=PartitionKey(path="/id"),
    offer_throughput=400
)

# ---- Fetch emails ----
def fetch_emails(token, top=5):
    url = f"https://graph.microsoft.com/v1.0/users/{USER_EMAIL}/messages?$top={top}&$select=id,subject,from,receivedDateTime,bodyPreview"
    headers = {"Authorization": f"Bearer {token}"}
    r = requests.get(url, headers=headers)
    r.raise_for_status()
    return r.json()["value"]

# ---- Fetch attachments for a specific email ----
def fetch_attachments(token, message_id):
    url = f"https://graph.microsoft.com/v1.0/users/{USER_EMAIL}/messages/{message_id}/attachments"
    headers = {"Authorization": f"Bearer {token}"}
    r = requests.get(url, headers=headers)
    r.raise_for_status()
    return r.json().get("value", [])

# ---- Store in Cosmos ----
def store_in_cosmos(item):
    container.upsert_item(item)

# ---- FastAPI app ----
app = FastAPI()

@app.get("/ingest-emails")
def ingest_emails():
    try:
        token = get_graph_token()
        emails = fetch_emails(token)

        thread_id = str(uuid.uuid4())
        for mail in emails:
            attachments = fetch_attachments(token, mail["id"])
            stored_item = {
                "id": str(uuid.uuid4()),
                "thread_id": thread_id,
                "subject": mail.get("subject"),
                "from": mail.get("from", {}).get("emailAddress", {}).get("address"),
                "receivedDateTime": mail.get("receivedDateTime"),
                "bodyPreview": mail.get("bodyPreview"),
                "attachments": []
            }
            for att in attachments:
                if att["@odata.type"] == "#microsoft.graph.fileAttachment":
                    stored_item["attachments"].append({
                        "filename": att["name"],
                        "fileContent": att["contentBytes"]  # Base64 string
                    })
            store_in_cosmos(stored_item)

        return {"status": "200 OK", "thread_id": thread_id}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
